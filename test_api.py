#!/usr/bin/env python3
"""
天气预报系统API测试脚本
用于测试后端API接口功能
"""

import requests
import json
from datetime import datetime, timedelta


class WeatherAPITester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.api_base = f"{base_url}/api"
    
    def test_health_check(self):
        """测试健康检查接口"""
        print("🔍 测试健康检查接口...")
        try:
            response = requests.get(f"{self.api_base}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查成功: {data['message']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_today_weather(self):
        """测试今日天气接口"""
        print("\n🌤️  测试今日天气接口...")
        try:
            response = requests.get(f"{self.api_base}/weather/today")
            data = response.json()
            
            if response.status_code == 200 and data['success']:
                weather = data['data']
                print(f"✅ 今日天气获取成功:")
                print(f"   日期: {weather['date']}")
                print(f"   天气: {weather['weather_condition']}")
                print(f"   温度: {weather['temperature_low']}°C - {weather['temperature_high']}°C")
                print(f"   湿度: {weather['humidity']}%")
                return True
            else:
                print(f"❌ 今日天气获取失败: {data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 今日天气接口异常: {e}")
            return False
    
    def test_recent_weather(self, days=7):
        """测试近期天气接口"""
        print(f"\n📅 测试近{days}日天气接口...")
        try:
            response = requests.get(f"{self.api_base}/weather/recent?days={days}")
            data = response.json()
            
            if response.status_code == 200 and data['success']:
                weather_list = data['data']
                print(f"✅ 近{days}日天气获取成功，共{len(weather_list)}条记录:")
                for weather in weather_list[:3]:  # 只显示前3条
                    print(f"   {weather['date']}: {weather['weather_condition']}, "
                          f"{weather['temperature_low']}-{weather['temperature_high']}°C")
                if len(weather_list) > 3:
                    print(f"   ... 还有{len(weather_list) - 3}条记录")
                return True
            else:
                print(f"❌ 近期天气获取失败: {data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 近期天气接口异常: {e}")
            return False
    
    def test_add_weather(self):
        """测试添加天气数据接口"""
        print("\n➕ 测试添加天气数据接口...")
        
        # 准备测试数据
        test_date = (datetime.now() + timedelta(days=10)).strftime('%Y-%m-%d')
        test_data = {
            'date': test_date,
            'temperature_high': 20,
            'temperature_low': 12,
            'humidity': 65,
            'weather_condition': '测试天气',
            'wind_speed': 8.5,
            'wind_direction': '东南风',
            'description': '这是一条测试天气数据'
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/weather",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            data = response.json()
            
            if response.status_code == 201 and data['success']:
                print(f"✅ 天气数据添加成功:")
                print(f"   日期: {test_data['date']}")
                print(f"   天气: {test_data['weather_condition']}")
                return True
            else:
                print(f"❌ 天气数据添加失败: {data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 添加天气数据异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        print("=" * 50)
        
        tests = [
            self.test_health_check,
            self.test_today_weather,
            lambda: self.test_recent_weather(7),
            lambda: self.test_recent_weather(3),
            self.test_add_weather
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️  部分测试失败，请检查服务器状态")
        
        return passed == total


def main():
    print("天气预报系统API测试工具")
    print("请确保服务器已启动 (python run.py)")
    print()
    
    tester = WeatherAPITester()
    success = tester.run_all_tests()
    
    if not success:
        print("\n💡 提示:")
        print("1. 确保服务器正在运行: python run.py")
        print("2. 确保数据库已初始化: python run.py --init-db")
        print("3. 检查端口5000是否被占用")


if __name__ == '__main__':
    main()
