#!/usr/bin/env python3
"""
天气预报系统启动脚本
支持初始化数据库和启动服务
"""

import sys
import os
import argparse
from backend.app import create_app
from backend.database import init_database


def main():
    parser = argparse.ArgumentParser(description='天气预报系统')
    parser.add_argument('--init-db', action='store_true', help='初始化数据库')
    parser.add_argument('--port', type=int, default=5000, help='服务端口号')
    parser.add_argument('--host', default='0.0.0.0', help='服务主机地址')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 创建Flask应用
    app = create_app()
    
    if args.init_db:
        print("正在初始化数据库...")
        with app.app_context():
            try:
                init_database()
                print("✅ 数据库初始化完成！")
            except Exception as e:
                print(f"❌ 数据库初始化失败: {e}")
                sys.exit(1)
        return
    
    # 启动服务
    print("🌤️  天气预报系统启动中...")
    print(f"📍 访问地址: http://localhost:{args.port}")
    print(f"🔗 API健康检查: http://localhost:{args.port}/api/health")
    print("📖 使用说明:")
    print("   - 访问主页查看天气信息")
    print("   - 使用 --init-db 参数初始化数据库")
    print("   - 使用 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        app.run(
            debug=args.debug,
            host=args.host,
            port=args.port,
            use_reloader=args.debug
        )
    except KeyboardInterrupt:
        print("\n👋 天气预报系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
