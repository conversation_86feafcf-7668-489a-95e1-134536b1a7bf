-- 天气预报系统测试数据
-- 插入近期天气数据用于测试

INSERT OR REPLACE INTO weather (date, temperature_high, temperature_low, humidity, weather_condition, wind_speed, wind_direction, description) VALUES
('2024-01-15', 8, 2, 65, '晴天', 12.5, '北风', '天气晴朗，适合户外活动'),
('2024-01-16', 10, 4, 58, '多云', 8.3, '东北风', '多云天气，温度适宜'),
('2024-01-17', 6, -1, 72, '小雨', 15.2, '东风', '小雨天气，注意保暖'),
('2024-01-18', 12, 5, 55, '晴天', 10.8, '南风', '阳光明媚，温度回升'),
('2024-01-19', 15, 8, 48, '晴天', 6.7, '西南风', '天气晴好，温度舒适'),
('2024-01-20', 9, 3, 68, '阴天', 11.4, '北风', '阴云密布，温度下降'),
('2024-01-21', 7, 1, 75, '小雪', 18.6, '西北风', '小雪天气，路面湿滑'),
('2024-01-22', 11, 4, 62, '多云', 9.2, '东南风', '多云转晴，温度适中'),
('2024-01-23', 14, 7, 52, '晴天', 7.8, '南风', '晴朗温暖，春意渐浓'),
('2024-01-24', 16, 9, 45, '晴天', 5.4, '西南风', '阳光充足，温度宜人');

-- 插入今天的天气数据（使用当前日期）
INSERT OR REPLACE INTO weather (date, temperature_high, temperature_low, humidity, weather_condition, wind_speed, wind_direction, description) VALUES
(date('now'), 13, 6, 60, '多云', 9.5, '东风', '今日多云，温度适宜，适合出行');

-- 插入未来几天的天气预报数据
INSERT OR REPLACE INTO weather (date, temperature_high, temperature_low, humidity, weather_condition, wind_speed, wind_direction, description) VALUES
(date('now', '+1 day'), 15, 8, 55, '晴天', 8.2, '南风', '明日晴朗，温度上升'),
(date('now', '+2 day'), 18, 11, 50, '晴天', 6.8, '西南风', '后天天气很好，温度舒适'),
(date('now', '+3 day'), 12, 5, 70, '小雨', 14.3, '东北风', '有小雨，注意携带雨具'),
(date('now', '+4 day'), 10, 3, 75, '阴天', 12.1, '北风', '阴天，温度较低'),
(date('now', '+5 day'), 14, 7, 58, '多云', 10.5, '东南风', '多云天气，温度回升'),
(date('now', '+6 day'), 17, 10, 48, '晴天', 7.6, '南风', '晴朗温暖，适合户外活动');
