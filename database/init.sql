-- 天气预报系统数据库初始化脚本
-- 创建天气信息表

CREATE TABLE IF NOT EXISTS weather (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL UNIQUE,
    temperature_high INTEGER NOT NULL,  -- 最高温度（摄氏度）
    temperature_low INTEGER NOT NULL,   -- 最低温度（摄氏度）
    humidity INTEGER NOT NULL,          -- 湿度（百分比）
    weather_condition VARCHAR(50) NOT NULL,  -- 天气状况
    wind_speed DECIMAL(5,2),           -- 风速（km/h）
    wind_direction VARCHAR(10),        -- 风向
    description TEXT,                  -- 天气描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建日期索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_weather_date ON weather(date);

-- 创建触发器，自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_weather_timestamp 
    AFTER UPDATE ON weather
    FOR EACH ROW
BEGIN
    UPDATE weather SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
