"""
天气预报系统Flask应用主文件
"""

import os
from flask import Flask, send_from_directory
from flask_cors import CORS
from backend.models import db
from backend.api import api_bp
from backend.database import get_database_config


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 配置数据库
    app.config.update(get_database_config())
    
    # 启用CORS，允许前端跨域访问
    CORS(app)
    
    # 初始化数据库
    db.init_app(app)
    
    # 注册API蓝图
    app.register_blueprint(api_bp)
    
    # 静态文件路由（前端文件）
    @app.route('/')
    def index():
        """主页路由"""
        frontend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_from_directory(frontend_dir, 'index.html')
    
    @app.route('/<path:filename>')
    def static_files(filename):
        """静态文件路由"""
        frontend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_from_directory(frontend_dir, filename)
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return {'success': False, 'message': '资源未找到'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'success': False, 'message': '服务器内部错误'}, 500
    
    return app


if __name__ == '__main__':
    app = create_app()
    
    # 在应用上下文中创建数据库表
    with app.app_context():
        db.create_all()
        print("数据库表创建成功")
    
    print("天气预报系统启动中...")
    print("访问地址: http://localhost:5000")
    print("API文档: http://localhost:5000/api/health")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
