"""
天气预报系统API路由
提供RESTful API接口
"""

from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
from backend.models import Weather, db

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')


@api_bp.route('/weather/today', methods=['GET'])
def get_today_weather():
    """获取今日天气"""
    try:
        weather = Weather.get_today_weather()
        
        if weather:
            return jsonify({
                'success': True,
                'data': weather.to_dict(),
                'message': '获取今日天气成功'
            })
        else:
            return jsonify({
                'success': False,
                'data': None,
                'message': '今日天气数据不存在'
            }), 404
    
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'获取今日天气失败: {str(e)}'
        }), 500


@api_bp.route('/weather/recent', methods=['GET'])
def get_recent_weather():
    """获取近期天气"""
    try:
        # 获取查询参数
        days = request.args.get('days', default=7, type=int)
        
        # 限制查询天数范围
        if days < 1:
            days = 1
        elif days > 30:
            days = 30
        
        weather_list = Weather.get_recent_weather(days)
        
        return jsonify({
            'success': True,
            'data': [weather.to_dict() for weather in weather_list],
            'message': f'获取近{days}日天气成功',
            'total': len(weather_list)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'获取近期天气失败: {str(e)}'
        }), 500


@api_bp.route('/weather/range', methods=['GET'])
def get_weather_by_range():
    """根据日期范围获取天气"""
    try:
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'data': None,
                'message': '请提供start_date和end_date参数'
            }), 400
        
        # 解析日期
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'data': None,
                'message': '日期格式错误，请使用YYYY-MM-DD格式'
            }), 400
        
        if start_date > end_date:
            return jsonify({
                'success': False,
                'data': None,
                'message': '开始日期不能大于结束日期'
            }), 400
        
        weather_list = Weather.get_weather_by_date_range(start_date, end_date)
        
        return jsonify({
            'success': True,
            'data': [weather.to_dict() for weather in weather_list],
            'message': f'获取{start_date}到{end_date}天气成功',
            'total': len(weather_list)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'获取天气范围数据失败: {str(e)}'
        }), 500


@api_bp.route('/weather', methods=['POST'])
def add_weather():
    """添加天气数据（用于测试）"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'data': None,
                'message': '请提供JSON数据'
            }), 400
        
        # 验证必需字段
        required_fields = ['date', 'temperature_high', 'temperature_low', 'humidity', 'weather_condition']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'data': None,
                    'message': f'缺少必需字段: {field}'
                }), 400
        
        # 解析日期
        try:
            date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'data': None,
                'message': '日期格式错误，请使用YYYY-MM-DD格式'
            }), 400
        
        # 创建天气记录
        weather = Weather(
            date=date,
            temperature_high=data['temperature_high'],
            temperature_low=data['temperature_low'],
            humidity=data['humidity'],
            weather_condition=data['weather_condition'],
            wind_speed=data.get('wind_speed'),
            wind_direction=data.get('wind_direction'),
            description=data.get('description')
        )
        
        db.session.add(weather)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': weather.to_dict(),
            'message': '天气数据添加成功'
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'data': None,
            'message': f'添加天气数据失败: {str(e)}'
        }), 500


@api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'success': True,
        'message': '天气预报API服务正常运行',
        'timestamp': datetime.now().isoformat()
    })
