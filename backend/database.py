"""
数据库配置和初始化模块
"""

import os
import sqlite3
from flask import current_app
from backend.models import db


def get_db_path():
    """获取数据库文件路径"""
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database', 'weather.db')


def init_database():
    """初始化数据库"""
    db_path = get_db_path()
    
    # 确保数据库目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 创建所有表
    with current_app.app_context():
        db.create_all()
        print(f"数据库表创建成功: {db_path}")
    
    # 执行初始化SQL脚本
    init_sql_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database', 'init.sql')
    if os.path.exists(init_sql_path):
        execute_sql_file(init_sql_path)
        print("数据库初始化脚本执行成功")
    
    # 执行测试数据SQL脚本
    test_data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database', 'test_data.sql')
    if os.path.exists(test_data_path):
        execute_sql_file(test_data_path)
        print("测试数据插入成功")


def execute_sql_file(sql_file_path):
    """执行SQL文件"""
    db_path = get_db_path()
    
    try:
        with sqlite3.connect(db_path) as conn:
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            # 分割SQL语句并执行
            statements = sql_script.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement:
                    conn.execute(statement)
            
            conn.commit()
            print(f"SQL文件执行成功: {sql_file_path}")
    
    except Exception as e:
        print(f"执行SQL文件时出错: {e}")
        raise


def get_database_config():
    """获取数据库配置"""
    db_path = get_db_path()
    return {
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'SQLALCHEMY_TRACK_MODIFICATIONS': False,
        'SQLALCHEMY_ECHO': False  # 设置为True可以看到SQL语句
    }
