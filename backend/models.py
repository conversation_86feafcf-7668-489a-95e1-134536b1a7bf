"""
天气预报系统数据模型
定义数据库表结构和ORM模型
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy import func

db = SQLAlchemy()


class Weather(db.Model):
    """天气信息模型"""
    
    __tablename__ = 'weather'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    date = db.Column(db.Date, nullable=False, unique=True, index=True)
    temperature_high = db.Column(db.Integer, nullable=False, comment='最高温度（摄氏度）')
    temperature_low = db.Column(db.Integer, nullable=False, comment='最低温度（摄氏度）')
    humidity = db.Column(db.Integer, nullable=False, comment='湿度（百分比）')
    weather_condition = db.Column(db.String(50), nullable=False, comment='天气状况')
    wind_speed = db.Column(db.Numeric(5, 2), comment='风速（km/h）')
    wind_direction = db.Column(db.String(10), comment='风向')
    description = db.Column(db.Text, comment='天气描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Weather {self.date}: {self.weather_condition}>'
    
    def to_dict(self):
        """将模型转换为字典格式，用于JSON序列化"""
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'temperature_high': self.temperature_high,
            'temperature_low': self.temperature_low,
            'humidity': self.humidity,
            'weather_condition': self.weather_condition,
            'wind_speed': float(self.wind_speed) if self.wind_speed else None,
            'wind_direction': self.wind_direction,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_today_weather(cls):
        """获取今日天气"""
        today = datetime.now().date()
        return cls.query.filter_by(date=today).first()
    
    @classmethod
    def get_recent_weather(cls, days=7):
        """获取近期天气，默认7天"""
        today = datetime.now().date()
        return cls.query.filter(
            cls.date <= today
        ).order_by(cls.date.desc()).limit(days).all()
    
    @classmethod
    def get_weather_by_date_range(cls, start_date, end_date):
        """根据日期范围获取天气"""
        return cls.query.filter(
            cls.date >= start_date,
            cls.date <= end_date
        ).order_by(cls.date.asc()).all()
