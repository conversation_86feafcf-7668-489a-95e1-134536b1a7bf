/* 天气预报系统样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 控制面板样式 */
.control-panel {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.query-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    justify-content: center;
}

.query-options {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.query-options label {
    font-weight: 600;
}

.query-options input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 80px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #0984e3;
    color: white;
}

.btn-primary:hover {
    background: #0770c2;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #636e72;
    color: white;
}

.btn-secondary:hover {
    background: #2d3436;
    transform: translateY(-2px);
}

.btn-success {
    background: #00b894;
    color: white;
}

.btn-success:hover {
    background: #00a085;
    transform: translateY(-2px);
}

/* 加载状态样式 */
.loading {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0984e3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误信息样式 */
.error {
    background: #ff7675;
    color: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 20px;
}

/* 天气区域样式 */
.weather-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.weather-section h2 {
    color: #2d3436;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.8rem;
}

/* 天气卡片样式 */
.weather-card {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    margin-bottom: 15px;
}

.today-card {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.weather-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.weather-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    text-align: center;
}

.info-item .label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.info-item .value {
    font-size: 1.3rem;
    font-weight: 600;
}

.temperature {
    font-size: 2rem !important;
}

.description {
    grid-column: 1 / -1;
    text-align: center;
    margin-top: 15px;
    font-style: italic;
    opacity: 0.9;
}

/* 天气列表样式 */
.weather-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* 底部样式 */
footer {
    text-align: center;
    color: white;
    margin-top: 30px;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .query-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .query-options {
        flex-direction: column;
        gap: 15px;
    }
    
    .weather-info {
        grid-template-columns: 1fr;
    }
    
    .weather-list {
        grid-template-columns: 1fr;
    }
}
