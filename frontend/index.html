<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气预报系统</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🌤️ 天气预报系统</h1>
            <p>查询当日和近期天气信息</p>
        </header>

        <main>
            <!-- 查询控制面板 -->
            <section class="control-panel">
                <div class="query-buttons">
                    <button id="todayBtn" class="btn btn-primary">今日天气</button>
                    <button id="recentBtn" class="btn btn-secondary">近期天气</button>
                </div>
                
                <div class="query-options" id="queryOptions" style="display: none;">
                    <label for="daysInput">查询天数:</label>
                    <input type="number" id="daysInput" min="1" max="30" value="7">
                    <button id="queryBtn" class="btn btn-success">查询</button>
                </div>
            </section>

            <!-- 加载状态 -->
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在加载天气数据...</p>
            </div>

            <!-- 错误信息 -->
            <div id="error" class="error" style="display: none;">
                <p id="errorMessage"></p>
            </div>

            <!-- 今日天气显示 -->
            <section id="todayWeather" class="weather-section" style="display: none;">
                <h2>今日天气</h2>
                <div id="todayWeatherCard" class="weather-card today-card">
                    <!-- 今日天气内容将通过JavaScript动态填充 -->
                </div>
            </section>

            <!-- 近期天气显示 -->
            <section id="recentWeather" class="weather-section" style="display: none;">
                <h2>近期天气</h2>
                <div id="recentWeatherList" class="weather-list">
                    <!-- 近期天气列表将通过JavaScript动态填充 -->
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 天气预报系统 | 基于Flask + SQLite构建</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
