// 天气预报系统前端JavaScript

class WeatherApp {
    constructor() {
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTodayWeather(); // 默认加载今日天气
    }

    bindEvents() {
        // 绑定按钮事件
        document.getElementById('todayBtn').addEventListener('click', () => {
            this.showTodayWeather();
        });

        document.getElementById('recentBtn').addEventListener('click', () => {
            this.showRecentWeatherOptions();
        });

        document.getElementById('queryBtn').addEventListener('click', () => {
            this.loadRecentWeather();
        });

        // 回车键查询
        document.getElementById('daysInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.loadRecentWeather();
            }
        });
    }

    showLoading() {
        this.hideAllSections();
        document.getElementById('loading').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        this.hideLoading();
        const errorDiv = document.getElementById('error');
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorDiv.style.display = 'block';
    }

    hideError() {
        document.getElementById('error').style.display = 'none';
    }

    hideAllSections() {
        document.getElementById('todayWeather').style.display = 'none';
        document.getElementById('recentWeather').style.display = 'none';
        document.getElementById('queryOptions').style.display = 'none';
        this.hideError();
    }

    showTodayWeather() {
        this.hideAllSections();
        this.loadTodayWeather();
    }

    showRecentWeatherOptions() {
        this.hideAllSections();
        document.getElementById('queryOptions').style.display = 'flex';
    }

    async loadTodayWeather() {
        this.showLoading();
        
        try {
            const response = await fetch(`${this.apiBase}/weather/today`);
            const result = await response.json();
            
            this.hideLoading();
            
            if (result.success && result.data) {
                this.displayTodayWeather(result.data);
            } else {
                this.showError(result.message || '获取今日天气失败');
            }
        } catch (error) {
            this.hideLoading();
            this.showError('网络请求失败，请检查服务器连接');
            console.error('Error:', error);
        }
    }

    async loadRecentWeather() {
        const days = document.getElementById('daysInput').value;
        
        if (!days || days < 1 || days > 30) {
            this.showError('请输入1-30之间的天数');
            return;
        }

        this.showLoading();
        
        try {
            const response = await fetch(`${this.apiBase}/weather/recent?days=${days}`);
            const result = await response.json();
            
            this.hideLoading();
            
            if (result.success) {
                this.displayRecentWeather(result.data, days);
            } else {
                this.showError(result.message || '获取近期天气失败');
            }
        } catch (error) {
            this.hideLoading();
            this.showError('网络请求失败，请检查服务器连接');
            console.error('Error:', error);
        }
    }

    displayTodayWeather(weather) {
        const todaySection = document.getElementById('todayWeather');
        const todayCard = document.getElementById('todayWeatherCard');
        
        todayCard.innerHTML = `
            <h3>${this.formatDate(weather.date)} - ${weather.weather_condition}</h3>
            <div class="weather-info">
                <div class="info-item">
                    <div class="label">最高温度</div>
                    <div class="value temperature">${weather.temperature_high}°C</div>
                </div>
                <div class="info-item">
                    <div class="label">最低温度</div>
                    <div class="value temperature">${weather.temperature_low}°C</div>
                </div>
                <div class="info-item">
                    <div class="label">湿度</div>
                    <div class="value">${weather.humidity}%</div>
                </div>
                <div class="info-item">
                    <div class="label">风速</div>
                    <div class="value">${weather.wind_speed || 'N/A'} km/h</div>
                </div>
                <div class="info-item">
                    <div class="label">风向</div>
                    <div class="value">${weather.wind_direction || 'N/A'}</div>
                </div>
                <div class="description">${weather.description || ''}</div>
            </div>
        `;
        
        todaySection.style.display = 'block';
    }

    displayRecentWeather(weatherList, days) {
        const recentSection = document.getElementById('recentWeather');
        const recentList = document.getElementById('recentWeatherList');
        
        // 更新标题
        recentSection.querySelector('h2').textContent = `近${days}日天气`;
        
        if (weatherList.length === 0) {
            recentList.innerHTML = '<p style="text-align: center; color: #666;">暂无天气数据</p>';
        } else {
            recentList.innerHTML = weatherList.map(weather => `
                <div class="weather-card">
                    <h3>${this.formatDate(weather.date)} - ${weather.weather_condition}</h3>
                    <div class="weather-info">
                        <div class="info-item">
                            <div class="label">最高温度</div>
                            <div class="value">${weather.temperature_high}°C</div>
                        </div>
                        <div class="info-item">
                            <div class="label">最低温度</div>
                            <div class="value">${weather.temperature_low}°C</div>
                        </div>
                        <div class="info-item">
                            <div class="label">湿度</div>
                            <div class="value">${weather.humidity}%</div>
                        </div>
                        <div class="info-item">
                            <div class="label">风速</div>
                            <div class="value">${weather.wind_speed || 'N/A'} km/h</div>
                        </div>
                        <div class="info-item">
                            <div class="label">风向</div>
                            <div class="value">${weather.wind_direction || 'N/A'}</div>
                        </div>
                        ${weather.description ? `<div class="description">${weather.description}</div>` : ''}
                    </div>
                </div>
            `).join('');
        }
        
        recentSection.style.display = 'block';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // 格式化为 YYYY-MM-DD 进行比较
        const formatDateForComparison = (d) => d.toISOString().split('T')[0];
        
        if (formatDateForComparison(date) === formatDateForComparison(today)) {
            return '今天';
        } else if (formatDateForComparison(date) === formatDateForComparison(yesterday)) {
            return '昨天';
        } else if (formatDateForComparison(date) === formatDateForComparison(tomorrow)) {
            return '明天';
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WeatherApp();
});
