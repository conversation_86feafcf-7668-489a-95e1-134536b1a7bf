# 快速启动指南

## 🚀 一键启动

### 1. 安装依赖
```bash
pip install -r backend/requirements.txt
```

### 2. 初始化数据库
```bash
python run.py --init-db
```

### 3. 启动服务
```bash
python run.py
```

### 4. 访问系统
打开浏览器访问: http://localhost:5000

## 🧪 测试API

运行API测试脚本：
```bash
python test_api.py
```

## 📋 功能说明

### 前端功能
- **今日天气**: 点击"今日天气"按钮查看当天天气信息
- **近期天气**: 点击"近期天气"按钮，可选择查询1-30天的历史天气

### API接口
- `GET /api/health` - 健康检查
- `GET /api/weather/today` - 获取今日天气
- `GET /api/weather/recent?days=N` - 获取近N日天气
- `POST /api/weather` - 添加天气数据

### 数据库
- 使用SQLite数据库存储在 `database/weather.db`
- 包含完整的测试数据
- 支持日期索引和自动时间戳更新

## 🛠️ 开发说明

### 项目结构
```
weather_system/
├── backend/           # 后端Flask应用
│   ├── app.py        # 主应用文件
│   ├── models.py     # 数据模型
│   ├── api.py        # API路由
│   ├── database.py   # 数据库配置
│   └── requirements.txt
├── frontend/          # 前端文件
│   ├── index.html    # 主页面
│   ├── style.css     # 样式文件
│   └── script.js     # JavaScript逻辑
├── database/          # 数据库相关
│   ├── init.sql      # 初始化脚本
│   ├── test_data.sql # 测试数据
│   └── weather.db    # SQLite数据库（运行时生成）
├── run.py            # 启动脚本
└── test_api.py       # API测试脚本
```

### 技术栈
- **后端**: Python 3.10 + Flask + SQLAlchemy
- **数据库**: SQLite
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **API**: RESTful JSON API

## 🔧 常见问题

### Q: 端口被占用怎么办？
A: 使用不同端口启动：`python run.py --port 8000`

### Q: 数据库初始化失败？
A: 检查database目录权限，确保可写入

### Q: 前端无法访问API？
A: 检查CORS配置，确保Flask-CORS已安装

### Q: 如何添加新的天气数据？
A: 使用POST接口或直接修改test_data.sql文件后重新初始化

## 📝 API使用示例

### 获取今日天气
```bash
curl http://localhost:5000/api/weather/today
```

### 获取近7日天气
```bash
curl http://localhost:5000/api/weather/recent?days=7
```

### 添加天气数据
```bash
curl -X POST http://localhost:5000/api/weather \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-01-25",
    "temperature_high": 15,
    "temperature_low": 8,
    "humidity": 60,
    "weather_condition": "晴天",
    "wind_speed": 10.5,
    "wind_direction": "南风",
    "description": "天气晴朗"
  }'
```
