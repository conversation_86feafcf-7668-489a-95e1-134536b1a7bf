# 天气预报系统

一个简单的前后端分离天气预报系统，使用Python Flask后端和HTML/JavaScript前端。

## 技术栈

- **后端**: Python 3.10 + Flask + SQLAlchemy
- **数据库**: SQLite
- **前端**: HTML + CSS + JavaScript
- **API**: RESTful JSON API

## 项目结构

```
weather_system/
├── backend/           # 后端代码
├── frontend/          # 前端代码
├── database/          # 数据库脚本和文件
├── README.md          # 项目说明
└── run.py            # 启动脚本
```

## 功能特性

- 查询当日天气信息
- 查询近期天气信息（可指定天数）
- 前后端分离架构
- RESTful API接口
- SQLite数据库存储

## 安装和运行

1. 安装依赖：
```bash
pip install -r backend/requirements.txt
```

2. 初始化数据库：
```bash
python run.py --init-db
```

3. 启动服务：
```bash
python run.py
```

4. 访问前端：
打开浏览器访问 `http://localhost:5000`

## API接口

- `GET /api/weather/today` - 获取当日天气
- `GET /api/weather/recent?days=N` - 获取近N日天气
- `POST /api/weather` - 添加天气数据

## 数据库

使用SQLite数据库，包含以下表：
- `weather`: 存储天气信息（日期、温度、湿度、天气状况等）

初始化脚本和测试数据位于 `database/` 目录。
